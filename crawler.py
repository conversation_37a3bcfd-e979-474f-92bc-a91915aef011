import traceback

import pandas as pd
import requests
import json


url_list = ["http://facorsa.no-ip.org:8070/api/Producto/",
            "http://facorsarosario.no-ip.org:8070/api/Producto/",
            "http://facorsaparana.no-ip.org:8070/api/Producto/",
            "http://facorsarafaela.no-ip.org:8070/api/Producto/",
            "http://facorsaran.no-ip.org:8070/api/Producto/"]


brand_list = [
 {'id': '0088', 'brand': 'AGRALE'},
 {'id': '0001', 'brand': 'ALFA ROMEO'},
 {'id': '0003', 'brand': 'ASIA'},
 {'id': '0004', 'brand': 'AUDI'},
 {'id': '0089', 'brand': 'BERNARDIN'},
 {'id': '0005', 'brand': 'BMW'},
 {'id': '0008', 'brand': 'CASE'},
 {'id': '0009', 'brand': 'CATERPILLAR'},
 {'id': '0092', 'brand': 'CHERY'},
 {'id': '0010', 'brand': 'CHEVROLET'},
 {'id': '0011', 'brand': 'CHEVROLET CAMIONES'},
 {'id': '0012', 'brand': 'CHRYSLER-JEEP'},
 {'id': '0013', 'brand': 'CITROEN'},
 {'id': '0014', 'brand': 'CLARK'},
 {'id': '0015', 'brand': 'CUMMINS'},
 {'id': '0016', 'brand': 'DACIA'},
 {'id': '0017', 'brand': 'DAEWOO'},
 {'id': '0018', 'brand': 'DAHIATSU'},
 {'id': '0085', 'brand': 'DIMEX'},
 {'id': '0020', 'brand': 'DODGE'},
 {'id': '0021', 'brand': 'DODGE CAMIONES'},
 {'id': '0091', 'brand': 'ESTACIONARIOS'},
 {'id': '0023', 'brand': 'FIAT'},
 {'id': '0024', 'brand': 'FIAT - CAMIONES'},
 {'id': '0025', 'brand': 'FIAT TRACTORES'},
 {'id': '0026', 'brand': 'FORD'},
 {'id': '0027', 'brand': 'FORD CAMIONES'},
 {'id': '0079', 'brand': 'FREIGHTLINER'},
 {'id': '0028', 'brand': 'HANOMAG'},
 {'id': '0029', 'brand': 'HONDA'},
 {'id': '0030', 'brand': 'HYUNDAI'},
 {'id': '0080', 'brand': 'INTERNATIONAL'},
 {'id': '0032', 'brand': 'ISUZU'},
 {'id': '0033', 'brand': 'IVECO'},
 {'id': '0034', 'brand': 'J.DEERE'},
 {'id': '0103', 'brand': 'JEEP'},
 {'id': '0101', 'brand': 'KAWASAKI'},
 {'id': '0083', 'brand': 'KENWORTH'},
 {'id': '0036', 'brand': 'KIA'},
 {'id': '0037', 'brand': 'KOMATSU'},
 {'id': '0038', 'brand': 'M.BENZ'},
 {'id': '0040', 'brand': 'M.BENZ AUTOMOVILES'},
 {'id': '0094', 'brand': 'MARANI AGRINAR'},
 {'id': '0043', 'brand': 'MASSEY F.'},
 {'id': '0044', 'brand': 'MAZDA'},
 {'id': '0022', 'brand': 'METALFOR'},
 {'id': '0047', 'brand': 'MICHIGAN'},
 {'id': '0048', 'brand': 'MITSUBISHI'},
 {'id': '0049', 'brand': 'NEW HOLLAND'},
 {'id': '0050', 'brand': 'NISSAN'},
 {'id': '0082', 'brand': 'PETERBILT'},
 {'id': '0054', 'brand': 'PEUGEOT'},
 {'id': '0055', 'brand': 'RASTROJERO'},
 {'id': '0056', 'brand': 'RENAULT - IKA'},
 {'id': '0090', 'brand': 'RENAULT CAMIONES'},
 {'id': '0057', 'brand': 'ROVER - LAND ROVER'},
 {'id': '0058', 'brand': 'SAMPI (Autoelev.)'},
 {'id': '0059', 'brand': 'SCANIA'},
 {'id': '0060', 'brand': 'SEAT'},
 {'id': '0063', 'brand': 'SUBARU'},
 {'id': '0064', 'brand': 'SUZUKI'},
 {'id': '0066', 'brand': 'TOYOTA'},
 {'id': '0068', 'brand': 'VALMET - AGCO- VALTRA'},
 {'id': '0069', 'brand': 'VARIOS'},
 {'id': '0072', 'brand': 'VARIOS AGRICOLAS'},
 {'id': '0071', 'brand': 'VARIOS MAQUINAS'},
 {'id': '0073', 'brand': 'VASALLI'},
 {'id': '0074', 'brand': 'VOLVO'},
 {'id': '0075', 'brand': 'VW'},
 {'id': '0076', 'brand': 'VW  CAMIONES'},
 {'id': '0102', 'brand': 'YAMAHA'},
 {'id': '0077', 'brand': 'ZANELLO - PAUNY'}]


headers = {
  'Accept': 'application/json, text/plain, */*',
  'Accept-Language': 'zh-CN,zh;q=0.9',
  'Cache-Control': 'no-cache',
  'Content-Type': 'application/json',
  'Origin': 'http://facorsa.no-ip.org',
  'Pragma': 'no-cache',
  'Proxy-Connection': 'keep-alive',
  'Referer': 'http://facorsa.no-ip.org/',
  'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
}


proxies = {
    "http": "http://127.0.0.1:7890"
}

result_list = []
try:
    for brand in brand_list:
        payload = json.dumps({
            "codigo": "",
            "detalle": "",
            "codigoDescripcion": "",
            "oem": "",
            "panel": "",
            "intercooler": "",
            "br": "",
            "visconde": "",
            "nombre_rubro": "RADIADORES",
            "nombre_sub_rubro": "RADIADORES",
            "nombre_marca": brand['brand'],
            "marcaProd": "",
            "nombre_modelo": "",
            "cod_marca": brand['id'],
            "marca": "",
            "exacto": False
        })
        for url in url_list:
            response = requests.request("POST", url, headers=headers, data=payload, proxies=proxies)

            result_list.extend(response.json())

        print(f"{brand['brand']} 已完成")

except Exception as e:
    traceback.print_exc()

finally:
    df = pd.DataFrame(result_list)
    df.to_excel("facorsa_data.xlsx", index=False)
